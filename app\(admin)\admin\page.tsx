import FileUpload from "@/components/file-upload";
import GalleryUpload from "@/components/gallery-upload";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   BookmarkSquareIcon,
   CloudArrowUpIcon,
   PhotoIcon,
   TagIcon,
} from "@heroicons/react/24/solid";

export default function AdminHomePage() {
   const stats = [
      {
         title: "Total Images",
         value: "0",
         description: "Images in gallery",
         icon: PhotoIcon,
      },
      {
         title: "Albums",
         value: "0",
         description: "Organized albums",
         icon: BookmarkSquareIcon,
      },
      {
         title: "Collections",
         value: "0",
         description: "Tagged collections",
         icon: TagIcon,
      },
      {
         title: "Recent Uploads",
         value: "0",
         description: "This month",
         icon: CloudArrowUpIcon,
      },
   ];

   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center space-x-4">
            <div>
               <h1 className="text-3xl font-bold text-foreground">
                  Gallery Dashboard
               </h1>
               <p className="text-muted-foreground">
                  Welcome to the Astral Studios gallery management system
               </p>
            </div>
         </div>

         {/* Stats Grid */}
         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat) => {
               const Icon = stat.icon;
               return (
                  <Card key={stat.title} className="border-border/50">
                     <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-muted-foreground">
                           {stat.title}
                        </CardTitle>
                        <Icon className="w-4 h-4 text-muted-foreground" />
                     </CardHeader>
                     <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                           {stat.value}
                        </div>
                        <p className="text-xs text-muted-foreground">
                           {stat.description}
                        </p>
                     </CardContent>
                  </Card>
               );
            })}
         </div>

         {/* Main Content Area */}
         <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="text-foreground">
                     Recent Activity
                  </CardTitle>
                  <CardDescription>
                     Latest gallery updates and changes
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <div className="flex items-center justify-center h-32 text-muted-foreground">
                     <div className="text-center">
                        <PhotoIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No recent activity</p>
                     </div>
                  </div>
               </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="text-foreground">
                     Quick Actions
                  </CardTitle>
                  <CardDescription>
                     Frequently used gallery management tools
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <div className="flex items-center justify-center h-32 text-muted-foreground">
                     <div className="text-center">
                        <CloudArrowUpIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Quick actions coming soon</p>
                     </div>
                  </div>
               </CardContent>
            </Card>
         </div>

         {/* File Upload */}
         <GalleryUpload />
         <div className="flex justify-center w-full">
            <FileUpload />
         </div>
      </div>
   );
}
