import { debugR2Configuration, testR2Connection, testSimpleR2Upload } from "@/lib/utils/debug-r2";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const test = searchParams.get("test") || "config";

    let result;
    
    switch (test) {
      case "config":
        result = await debugR2Configuration();
        break;
      case "connection":
        result = await testR2Connection();
        break;
      case "upload":
        result = await testSimpleR2Upload();
        break;
      default:
        return NextResponse.json({
          error: "Invalid test type. Use: config, connection, or upload"
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      test,
      result
    });
  } catch (error) {
    console.error("Debug API error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      details: {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      }
    }, { status: 500 });
  }
}
