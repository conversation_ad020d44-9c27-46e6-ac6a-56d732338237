"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>con, Trash2, UploadIcon, XIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import { formatBytes, useFileUpload } from "@/hooks/use-file-upload";
import { PhotoIcon } from "@heroicons/react/24/solid";
import Image from "next/image";

export default function FileUpload() {
   const maxSizeMB = 5;
   const maxSize = maxSizeMB * 1024 * 1024; // 5MB default
   const maxFiles = 6;

   const [
      { files, isDragging, errors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         clearFiles,
         getInputProps,
      },
   ] = useFileUpload({
      accept: "image/svg+xml,image/png,image/jpeg,image/jpg,image/gif",
      maxSize,
      multiple: true,
      maxFiles,
   });

   return (
      <div className="flex flex-col gap-2 w-full">
         {/* Drop area */}
         <div
            onClick={files.length > 0 ? undefined : openFileDialog}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            data-dragging={isDragging || undefined}
            data-files={files.length > 0 || undefined}
            className={`border-border data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 relative flex min-h-62 flex-col items-center overflow-hidden rounded-xl border-2 border-dashed p-4 transition-colors not-data-[files]:justify-center has-[input:focus]:ring-[3px] hover:bg-astral-grey/50 ${
               files.length > 0 ? "cursor-default" : "cursor-pointer"
            }`}
         >
            <input
               {...getInputProps()}
               className="sr-only"
               aria-label="Upload image file"
            />
            {files.length > 0 ? (
               <div className="flex w-full flex-col gap-4">
                  <div className="flex items-center justify-between gap-2">
                     <h3 className="truncate text-sm font-medium">
                        Selected Files ({files.length})
                     </h3>
                     <div className="flex items-center gap-3 overflow-x-auto">
                        <Button
                           variant="outline"
                           size="sm"
                           disabled={files.length >= maxFiles}
                           className="rounded-lg py-2 !px-4 h-auto"
                        >
                           <UploadIcon
                              className="-ms-0.5 mr-1 size-4 opacity-60"
                              aria-hidden="true"
                           />
                           Upload Images
                        </Button>
                        <Button
                           variant="outline"
                           size="sm"
                           onClick={openFileDialog}
                           disabled={files.length >= maxFiles}
                           className="rounded-lg py-2 !px-4 h-auto"
                        >
                           <UploadIcon
                              className="-ms-0.5 mr-1 size-4 opacity-60"
                              aria-hidden="true"
                           />
                           Add more
                        </Button>
                        <Button
                           variant="outline"
                           size="sm"
                           onClick={clearFiles}
                           disabled={files.length >= maxFiles}
                           className="rounded-lg py-2 !px-4 h-auto"
                        >
                           <Trash2
                              className="-ms-0.5 mr-1 size-4 opacity-60"
                              aria-hidden="true"
                           />
                           Remove all
                        </Button>
                     </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                     {files.map((file) => (
                        <div
                           className="flex flex-col aspect-square w-full"
                           key={file.id}
                        >
                           <div className="bg-astral-grey/50 relative w-full h-[calc(100%-62.5px)] rounded-md rounded-b-none">
                              <Image
                                 src={file.preview || ""}
                                 alt={file.file.name}
                                 fill
                                 className="size-full rounded-[inherit] object-cover"
                              />
                              <Button
                                 onClick={() => removeFile(file.id)}
                                 size="icon"
                                 className="border-background focus-visible:border-background absolute -top-2 -right-2 size-6 rounded-full border-2 shadow-none"
                                 aria-label="Remove image"
                              >
                                 <XIcon className="size-3.5" />
                              </Button>
                           </div>
                           <div className="flex min-w-0 flex-col gap-0.5 rounded-b-md p-3 bg-astral-grey/80">
                              <p className="truncate text-[13px] font-medium">
                                 {file.file.name}
                              </p>
                              <p className="text-muted-foreground truncate text-xs">
                                 {formatBytes(file.file.size)}
                              </p>
                           </div>
                        </div>
                     ))}
                  </div>
               </div>
            ) : (
               <div className="flex flex-col items-center justify-center px-4 py-3 text-center">
                  <div
                     className="bg-background mb-2 flex size-12 shrink-0 items-center justify-center rounded-full border"
                     aria-hidden="true"
                  >
                     <PhotoIcon className="size-5 opacity-60" />
                  </div>
                  <p className="mb-1.5 text-sm font-medium">
                     Upload images to your gallery
                  </p>
                  <p className="mb-1.5 text-sm text-muted-foreground">
                     Drag & drop or click to browse files
                  </p>
                  <p className="text-muted-foreground/60 text-xs">
                     SVG, PNG, JPG or GIF (max. {maxSizeMB}MB)
                  </p>
               </div>
            )}
         </div>

         {errors.length > 0 && (
            <div
               className="text-destructive flex items-center gap-1 text-xs"
               role="alert"
            >
               <AlertCircleIcon className="size-3 shrink-0" />
               <span>{errors[0]}</span>
            </div>
         )}
      </div>
   );
}
