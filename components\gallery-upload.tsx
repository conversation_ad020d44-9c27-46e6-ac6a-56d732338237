"use client";

import {
  Alert<PERSON>ircleIcon,
  CheckCircle,
  RefreshCw,
  Trash2,
  UploadIcon,
  X,
  XIcon,
  Loader2,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useGalleryUpload, type GalleryUploadOptions } from "@/hooks/use-gallery-upload";
import { PhotoIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import { formatFileSize } from "@/lib/utils/image-processing";

interface GalleryUploadProps {
  options?: GalleryUploadOptions;
  className?: string;
}

export default function GalleryUpload({ options, className }: GalleryUploadProps) {
  const [
    { files, isUploading, errors, isDragging },
    {
      addFiles,
      removeFile,
      clearFiles,
      uploadFile,
      uploadAll,
      cancelUpload,
      cancelAllUploads,
      retryUpload,
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      getInputProps,
    },
  ] = useGalleryUpload(options);

  const pendingFiles = files.filter(f => f.status === 'pending' || f.status === 'error');
  const uploadingFiles = files.filter(f => f.status === 'uploading');
  const successFiles = files.filter(f => f.status === 'success');

  const getStatusIcon = (status: string, progress: number) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="size-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="size-4 text-green-500" />;
      case 'error':
        return <AlertCircleIcon className="size-4 text-red-500" />;
      case 'cancelled':
        return <X className="size-4 text-gray-500" />;
      default:
        return <UploadIcon className="size-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploading':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'cancelled':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  return (
    <div className={`flex flex-col gap-4 w-full ${className}`}>
      {/* Drop area */}
      <div
        onClick={files.length > 0 ? undefined : openFileDialog}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        data-dragging={isDragging || undefined}
        data-files={files.length > 0 || undefined}
        className={`border-border data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 relative flex min-h-62 flex-col items-center overflow-hidden rounded-xl border-2 border-dashed p-4 transition-colors not-data-[files]:justify-center has-[input:focus]:ring-[3px] hover:bg-astral-grey/50 ${
          files.length > 0 ? "cursor-default" : "cursor-pointer"
        }`}
      >
        <input {...getInputProps()} className="sr-only" />

        {files.length === 0 ? (
          <>
            <PhotoIcon className="size-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Upload Images to Gallery
            </h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Drag and drop your images here, or click to browse
            </p>
            <p className="text-xs text-muted-foreground text-center">
              Supports JPEG, PNG, GIF, WebP, and SVG files up to{" "}
              {formatFileSize(options?.maxSizeBytes || 10 * 1024 * 1024)}
            </p>
          </>
        ) : (
          <div className="w-full">
            {/* Upload controls */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Button
                  onClick={openFileDialog}
                  variant="outline"
                  size="sm"
                  disabled={isUploading}
                >
                  <PhotoIcon className="size-4 mr-2" />
                  Add More Images
                </Button>
                
                {pendingFiles.length > 0 && (
                  <Button
                    onClick={uploadAll}
                    size="sm"
                    disabled={isUploading}
                  >
                    <UploadIcon className="size-4 mr-2" />
                    Upload All ({pendingFiles.length})
                  </Button>
                )}
                
                {isUploading && (
                  <Button
                    onClick={cancelAllUploads}
                    variant="outline"
                    size="sm"
                  >
                    <X className="size-4 mr-2" />
                    Cancel Uploads
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {successFiles.length}/{files.length} uploaded
                </span>
                <Button
                  onClick={clearFiles}
                  variant="ghost"
                  size="sm"
                  disabled={isUploading}
                >
                  <Trash2 className="size-4" />
                </Button>
              </div>
            </div>

            {/* Upload progress summary */}
            {isUploading && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">
                    Uploading {uploadingFiles.length} files...
                  </span>
                  <span className="text-sm text-blue-700">
                    {Math.round((successFiles.length / files.length) * 100)}%
                  </span>
                </div>
                <Progress 
                  value={(successFiles.length / files.length) * 100} 
                  className="h-2"
                />
              </div>
            )}

            {/* Files grid */}
            <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
              {files.map((fileItem) => (
                <div
                  key={fileItem.id}
                  className={`relative flex flex-col rounded-lg border-2 transition-all ${getStatusColor(fileItem.status)}`}
                >
                  {/* Image preview */}
                  <div className="relative aspect-square w-full rounded-t-md overflow-hidden">
                    <Image
                      src={fileItem.preview}
                      alt={fileItem.file.name}
                      fill
                      className="object-cover"
                    />
                    
                    {/* Status overlay */}
                    <div className="absolute top-2 left-2">
                      {getStatusIcon(fileItem.status, fileItem.progress)}
                    </div>

                    {/* Remove button */}
                    <Button
                      onClick={() => removeFile(fileItem.id)}
                      size="icon"
                      variant="destructive"
                      className="absolute top-2 right-2 size-6 rounded-full"
                      disabled={fileItem.status === 'uploading'}
                    >
                      <XIcon className="size-3" />
                    </Button>

                    {/* Progress overlay for uploading files */}
                    {fileItem.status === 'uploading' && (
                      <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-2">
                        <Progress value={fileItem.progress} className="h-1" />
                      </div>
                    )}
                  </div>

                  {/* File info and actions */}
                  <div className="p-3 space-y-2">
                    <div className="min-w-0">
                      <p className="text-xs font-medium truncate" title={fileItem.file.name}>
                        {fileItem.file.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(fileItem.file.size)}
                        {fileItem.metadata && (
                          <span className="ml-1">
                            • {fileItem.metadata.width}×{fileItem.metadata.height}
                          </span>
                        )}
                      </p>
                    </div>

                    {/* Action buttons */}
                    <div className="flex gap-1">
                      {fileItem.status === 'pending' && (
                        <Button
                          onClick={() => uploadFile(fileItem.id)}
                          size="sm"
                          variant="outline"
                          className="flex-1 h-7 text-xs"
                        >
                          <UploadIcon className="size-3 mr-1" />
                          Upload
                        </Button>
                      )}
                      
                      {fileItem.status === 'error' && (
                        <Button
                          onClick={() => retryUpload(fileItem.id)}
                          size="sm"
                          variant="outline"
                          className="flex-1 h-7 text-xs"
                        >
                          <RefreshCw className="size-3 mr-1" />
                          Retry
                        </Button>
                      )}
                      
                      {fileItem.status === 'uploading' && (
                        <Button
                          onClick={() => cancelUpload(fileItem.id)}
                          size="sm"
                          variant="outline"
                          className="flex-1 h-7 text-xs"
                        >
                          <X className="size-3 mr-1" />
                          Cancel
                        </Button>
                      )}
                      
                      {fileItem.status === 'success' && (
                        <div className="flex-1 h-7 flex items-center justify-center text-xs text-green-600 font-medium">
                          <CheckCircle className="size-3 mr-1" />
                          Uploaded
                        </div>
                      )}
                    </div>

                    {/* Error message */}
                    {fileItem.error && (
                      <p className="text-xs text-red-600 mt-1" title={fileItem.error}>
                        {fileItem.error.length > 50 
                          ? `${fileItem.error.substring(0, 50)}...` 
                          : fileItem.error
                        }
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Error messages */}
      {errors.length > 0 && (
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <div className="flex items-start">
            <AlertCircleIcon className="size-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800 mb-1">
                Upload Errors
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
