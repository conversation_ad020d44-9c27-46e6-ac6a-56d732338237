"use server";

import {
   deleteFile as deleteFileFromR2,
   uploadFileFromFile,
} from "@/lib/cloudflare/cloudflare";
import { ApiResponse, CreateImageInput } from "@/lib/models";
import { createImage } from "@/lib/services/image-service";
import { validateImageFile } from "@/lib/utils/image-processing";

/**
 * Upload image to Cloudflare R2 and save metadata to database
 */
export async function uploadImage(formData: FormData): Promise<ApiResponse> {
   try {
      const file = formData.get("file") as File;
      const albumId = formData.get("albumId") as string | null;
      const collectionId = formData.get("collectionId") as string | null;
      const width = parseInt(formData.get("width") as string);
      const height = parseInt(formData.get("height") as string);

      if (!file) {
         return {
            success: false,
            error: "No file provided",
         };
      }

      // Validate file
      const validationErrors = validateImageFile(file);
      if (validationErrors.length > 0) {
         return {
            success: false,
            error: validationErrors.join(", "),
         };
      }

      // Upload to R2
      const { url: imageUrl, key } = await uploadFileFromFile(file);

      // Create image metadata
      const imageInput: CreateImageInput = {
         url: imageUrl,
         name: file.name,
         albumId: albumId || null,
         collectionId: collectionId || null,
         width,
         height,
         fileSize: file.size,
         mimeType: file.type,
      };

      // Save to database
      const savedImage = await createImage(imageInput);

      return {
         success: true,
         data: savedImage,
         message: "Image uploaded successfully",
      };
   } catch (error) {
      console.error("Error uploading image:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to upload image",
      };
   }
}

/**
 * Upload multiple images
 */
export async function uploadMultipleImages(
   formData: FormData
): Promise<ApiResponse> {
   try {
      const files = formData.getAll("files") as File[];
      const albumId = formData.get("albumId") as string | null;
      const collectionId = formData.get("collectionId") as string | null;

      if (!files || files.length === 0) {
         return {
            success: false,
            error: "No files provided",
         };
      }

      const results = [];
      const errors = [];

      for (const file of files) {
         try {
            // Get dimensions from form data (should be provided by client)
            const width = parseInt(
               formData.get(`width_${file.name}`) as string
            );
            const height = parseInt(
               formData.get(`height_${file.name}`) as string
            );

            const singleFormData = new FormData();
            singleFormData.append("file", file);
            singleFormData.append("albumId", albumId || "");
            singleFormData.append("collectionId", collectionId || "");
            singleFormData.append("width", width.toString());
            singleFormData.append("height", height.toString());

            const result = await uploadImage(singleFormData);

            if (result.success) {
               results.push(result.data);
            } else {
               errors.push(`${file.name}: ${result.error}`);
            }
         } catch (error) {
            errors.push(
               `${file.name}: ${
                  error instanceof Error ? error.message : "Unknown error"
               }`
            );
         }
      }

      if (errors.length > 0 && results.length === 0) {
         return {
            success: false,
            error: `All uploads failed: ${errors.join(", ")}`,
         };
      }

      return {
         success: true,
         data: {
            uploaded: results,
            errors: errors.length > 0 ? errors : undefined,
         },
         message: `Successfully uploaded ${results.length} of ${files.length} images`,
      };
   } catch (error) {
      console.error("Error uploading multiple images:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to upload images",
      };
   }
}

/**
 * Delete image from R2 and database
 */
export async function deleteImageAction(imageId: string): Promise<ApiResponse> {
   try {
      // First get the image to get the R2 key
      const { getImageById, deleteImage } = await import(
         "@/lib/services/image-service"
      );
      const image = await getImageById(imageId);

      if (!image) {
         return {
            success: false,
            error: "Image not found",
         };
      }

      // Extract key from URL
      const url = new URL(image.url);
      const key = url.pathname.substring(1); // Remove leading slash

      // Delete from R2
      await deleteFileFromR2(key);

      // Delete from database
      const deleted = await deleteImage(imageId);

      if (!deleted) {
         return {
            success: false,
            error: "Failed to delete image from database",
         };
      }

      return {
         success: true,
         message: "Image deleted successfully",
      };
   } catch (error) {
      console.error("Error deleting image:", error);
      return {
         success: false,
         error:
            error instanceof Error ? error.message : "Failed to delete image",
      };
   }
}
