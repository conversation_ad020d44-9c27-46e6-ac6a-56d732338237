"use server";

import { S3Client, PutObjectCommand, ListObjectsV2Command } from "@aws-sdk/client-s3";

const { R2_ACCESS_ID, R2_ACCESS_KEY, R2_ACCOUNT_ID, R2_BUCKET } = process.env;

// Minimal R2 client configuration
const R2_MINIMAL = new S3Client({
  region: "auto",
  endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: R2_ACCESS_ID!,
    secretAccessKey: R2_ACCESS_KEY!,
  },
});

export async function testMinimalUpload() {
  try {
    console.log("Testing minimal R2 configuration...");
    
    // Test with a simple list operation first
    const listCommand = new ListObjectsV2Command({
      Bucket: R2_BUCKET,
      MaxKeys: 1,
    });
    
    const listResult = await R2_MINIMAL.send(listCommand);
    console.log("✅ List operation successful");
    
    // Test upload
    const testContent = Buffer.from("Test content", "utf-8");
    const uploadCommand = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: `test/minimal-test-${Date.now()}.txt`,
      Body: testContent,
      ContentType: "text/plain",
    });
    
    const uploadResult = await R2_MINIMAL.send(uploadCommand);
    console.log("✅ Upload operation successful");
    
    return { success: true, listResult, uploadResult };
  } catch (error) {
    console.error("❌ Minimal test failed:", error);
    return { success: false, error };
  }
}
