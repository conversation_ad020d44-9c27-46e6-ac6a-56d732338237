"use server";

import {
   DeleteObjectCommand,
   GetO<PERSON>Command,
   ListObjectsV2Command,
   PutObjectCommand,
   S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { generateUniqueKey } from "./helpers";

const { R2_ACCESS_ID, R2_ACCESS_KEY, R2_ACCOUNT_ID, R2_ENDPOINT, R2_BUCKET } =
   process.env;

if (
   !R2_ACCESS_ID ||
   !R2_ACCESS_KEY ||
   !R2_ACCOUNT_ID ||
   !R2_ENDPOINT ||
   !R2_BUCKET
) {
   throw new Error("Missing Cloudflare environment variables");
}

export interface FileObject {
   Key?: string;
   LastModified?: Date;
   ETag?: string;
   Size?: number;
   StorageClass?: string;
}

// Ensure the endpoint has the correct format for Cloudflare R2
const endpoint = R2_ENDPOINT.startsWith("https://")
   ? R2_ENDPOINT
   : `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;

const R2 = new S3Client({
   region: "auto",
   endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
   credentials: {
      accessKeyId: R2_ACCESS_ID,
      secretAccessKey: R2_ACCESS_KEY,
   },
   forcePathStyle: false, // Cloudflare R2 uses virtual-hosted-style
   maxAttempts: 3, // Retry failed requests
   requestHandler: {
      connectionTimeout: 30000, // 30 seconds
      socketTimeout: 30000, // 30 seconds
   },
});

/**
 * Upload file buffer to R2 with specified key
 */
export async function uploadFile(
   file: Buffer,
   key: string,
   contentType?: string
) {
   const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
      Body: file,
      ContentType: contentType,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
   }
}

/**
 * Upload file directly from File object
 */
export async function uploadFileFromFile(
   file: File,
   key?: string
): Promise<{ url: string; key: string }> {
   try {
      console.log("Cloudflare R2: Starting upload for file:", file.name);
      console.log("Cloudflare R2: Endpoint:", endpoint);
      console.log("Cloudflare R2: Account ID:", R2_ACCOUNT_ID);
      console.log("Cloudflare R2: Bucket:", R2_BUCKET);

      const fileKey = key || generateUniqueKey(file.name);
      console.log("Cloudflare R2: Generated key:", fileKey);

      const buffer = Buffer.from(await file.arrayBuffer());
      console.log("Cloudflare R2: Buffer size:", buffer.length);

      await uploadFile(buffer, fileKey, file.type);

      // Generate public URL
      const url = `https://${R2_BUCKET}.${R2_ACCOUNT_ID}.r2.cloudflarestorage.com/${key}`;
      console.log("Cloudflare R2: Generated URL:", url);

      return { url, key: fileKey };
   } catch (error) {
      console.error("Cloudflare R2: Upload failed:", error);
      console.error("Cloudflare R2: Error details:", {
         name: error instanceof Error ? error.name : "Unknown",
         message: error instanceof Error ? error.message : "Unknown error",
         stack: error instanceof Error ? error.stack : "No stack trace",
      });
      throw error;
   }
}

export async function getSignedUrlForUpload(file: File) {
   const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: file.name,
      Body: file,
   });

   try {
      const signedUrl = await getSignedUrl(R2, command, {
         expiresIn: 60 * 60 * 4, // 4 hours
      });
      return signedUrl;
   } catch (error) {
      console.error("Error generating signed URL:", error);
      throw error;
   }
}

export async function getSignedUrlForDownload(key: string): Promise<string> {
   const command = new GetObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
   });

   try {
      const signedUrl = await getSignedUrl(R2, command, { expiresIn: 3600 });
      return signedUrl;
   } catch (error) {
      console.error("Error generating signed URL:", error);
      throw error;
   }
}

export async function listFiles(prefix: string = ""): Promise<FileObject[]> {
   const command = new ListObjectsV2Command({
      Bucket: R2_BUCKET,
      Prefix: prefix,
   });

   try {
      const response = await R2.send(command);
      return response.Contents || [];
   } catch (error) {
      console.error("Error listing files:", error);
      throw error;
   }
}

export async function deleteFile(key: string) {
   const command = new DeleteObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error deleting file:", error);
      throw error;
   }
}
