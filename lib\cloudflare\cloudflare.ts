"use server";

import {
   DeleteObjectCommand,
   GetObjectCommand,
   ListObjectsV2Command,
   PutObjectCommand,
   S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { generateUniqueKey } from "./helpers";

const { R2_ACCESS_ID, R2_ACCESS_KEY, R2_ACCOUNT_ID, R2_ENDPOINT, R2_BUCKET } =
   process.env;

if (
   !R2_ACCESS_ID ||
   !R2_ACCESS_KEY ||
   !R2_ACCOUNT_ID ||
   !R2_ENDPOINT ||
   !R2_BUCKET
) {
   throw new Error("Missing Cloudflare environment variables");
}

export interface FileObject {
   Key?: string;
   LastModified?: Date;
   ETag?: string;
   Size?: number;
   StorageClass?: string;
}

const R2 = new S3Client({
   region: "auto",
   endpoint: R2_ENDPOINT,
   credentials: {
      accessKeyId: R2_ACCESS_ID,
      secretAccessKey: R2_ACCESS_KEY,
   },
});

/**
 * Upload file buffer to R2 with specified key
 */
export async function uploadFile(
   file: Buffer,
   key: string,
   contentType?: string
) {
   const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
      Body: file,
      ContentType: contentType,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
   }
}

/**
 * Upload file directly from File object
 */
export async function uploadFileFromFile(
   file: File,
   key?: string
): Promise<{ url: string; key: string }> {
   const fileKey = key || generateUniqueKey(file.name);
   const buffer = Buffer.from(await file.arrayBuffer());

   await uploadFile(buffer, fileKey, file.type);

   // Generate public URL
   const url = `https://${R2_BUCKET}.${R2_ACCOUNT_ID}.r2.cloudflarestorage.com/${key}`;

   return { url, key: fileKey };
}

export async function getSignedUrlForUpload(file: File) {
   const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: file.name,
      Body: file,
   });

   try {
      const signedUrl = await getSignedUrl(R2, command, {
         expiresIn: 60 * 60 * 4, // 4 hours
      });
      return signedUrl;
   } catch (error) {
      console.error("Error generating signed URL:", error);
      throw error;
   }
}

export async function getSignedUrlForDownload(key: string): Promise<string> {
   const command = new GetObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
   });

   try {
      const signedUrl = await getSignedUrl(R2, command, { expiresIn: 3600 });
      return signedUrl;
   } catch (error) {
      console.error("Error generating signed URL:", error);
      throw error;
   }
}

export async function listFiles(prefix: string = ""): Promise<FileObject[]> {
   const command = new ListObjectsV2Command({
      Bucket: R2_BUCKET,
      Prefix: prefix,
   });

   try {
      const response = await R2.send(command);
      return response.Contents || [];
   } catch (error) {
      console.error("Error listing files:", error);
      throw error;
   }
}

export async function deleteFile(key: string) {
   const command = new DeleteObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error deleting file:", error);
      throw error;
   }
}
