"use server";

import { getCollection } from "@/lib/mongodb/db";
import { 
  Image, 
  CreateImageInput, 
  UpdateImageInput, 
  PaginationOptions, 
  PaginatedResponse,
  createPaginationMetadata,
  DEFAULT_PAGINATION,
  createImageMetadata,
  validateImageInput
} from "@/lib/models";
import { ObjectId } from "mongodb";

/**
 * Get all images with pagination and filtering
 */
export async function getImages(options: PaginationOptions & {
  albumId?: string | null;
  collectionId?: string | null;
} = {}): Promise<PaginatedResponse<Image>> {
  try {
    const {
      page = DEFAULT_PAGINATION.page,
      limit = DEFAULT_PAGINATION.limit,
      sortBy = DEFAULT_PAGINATION.sortBy,
      sortOrder = DEFAULT_PAGINATION.sortOrder,
      albumId,
      collectionId,
    } = options;

    const collection = await getCollection<Image>("images");
    
    // Build filter query
    const filter: any = {};
    
    if (albumId !== undefined) {
      filter.albumId = albumId;
    }
    
    if (collectionId !== undefined) {
      filter.collectionId = collectionId;
    }

    // Build sort query
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Execute queries
    const [images, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    const pagination = createPaginationMetadata(page, limit, total);

    return {
      data: images,
      pagination,
    };
  } catch (error) {
    console.error("Error fetching images:", error);
    throw new Error("Failed to fetch images");
  }
}

/**
 * Get ungrouped images (albumId is null)
 */
export async function getUngroupedImages(options: PaginationOptions = {}): Promise<PaginatedResponse<Image>> {
  return getImages({ ...options, albumId: null });
}

/**
 * Get image by ID
 */
export async function getImageById(id: string): Promise<Image | null> {
  try {
    const collection = await getCollection<Image>("images");
    const image = await collection.findOne({ _id: new ObjectId(id) });
    return image;
  } catch (error) {
    console.error("Error fetching image by ID:", error);
    throw new Error("Failed to fetch image");
  }
}

/**
 * Create a new image
 */
export async function createImage(input: CreateImageInput): Promise<Image> {
  try {
    // Validate input
    const errors = validateImageInput(input);
    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }

    const collection = await getCollection<Image>("images");
    const imageData = createImageMetadata(input);
    
    const result = await collection.insertOne(imageData);
    
    if (!result.insertedId) {
      throw new Error("Failed to create image");
    }

    const createdImage = await collection.findOne({ _id: result.insertedId });
    
    if (!createdImage) {
      throw new Error("Failed to retrieve created image");
    }

    return createdImage;
  } catch (error) {
    console.error("Error creating image:", error);
    throw error;
  }
}

/**
 * Update an image
 */
export async function updateImage(id: string, input: UpdateImageInput): Promise<Image | null> {
  try {
    const collection = await getCollection<Image>("images");
    
    const updateData = {
      ...input,
      updatedAt: new Date(),
    };

    const result = await collection.findOneAndUpdate(
      { _id: new ObjectId(id) },
      { $set: updateData },
      { returnDocument: 'after' }
    );

    return result || null;
  } catch (error) {
    console.error("Error updating image:", error);
    throw new Error("Failed to update image");
  }
}

/**
 * Delete an image
 */
export async function deleteImage(id: string): Promise<boolean> {
  try {
    const collection = await getCollection<Image>("images");
    const result = await collection.deleteOne({ _id: new ObjectId(id) });
    return result.deletedCount > 0;
  } catch (error) {
    console.error("Error deleting image:", error);
    throw new Error("Failed to delete image");
  }
}

/**
 * Get images by album ID
 */
export async function getImagesByAlbumId(albumId: string, options: PaginationOptions = {}): Promise<PaginatedResponse<Image>> {
  return getImages({ ...options, albumId });
}

/**
 * Get images by collection ID
 */
export async function getImagesByCollectionId(collectionId: string, options: PaginationOptions = {}): Promise<PaginatedResponse<Image>> {
  return getImages({ ...options, collectionId });
}

/**
 * Search images by name
 */
export async function searchImages(query: string, options: PaginationOptions = {}): Promise<PaginatedResponse<Image>> {
  try {
    const {
      page = DEFAULT_PAGINATION.page,
      limit = DEFAULT_PAGINATION.limit,
      sortBy = DEFAULT_PAGINATION.sortBy,
      sortOrder = DEFAULT_PAGINATION.sortOrder,
    } = options;

    const collection = await getCollection<Image>("images");
    
    // Build search filter
    const filter = {
      name: { $regex: query, $options: 'i' }
    };

    // Build sort query
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Execute queries
    const [images, total] = await Promise.all([
      collection
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .toArray(),
      collection.countDocuments(filter),
    ]);

    const pagination = createPaginationMetadata(page, limit, total);

    return {
      data: images,
      pagination,
    };
  } catch (error) {
    console.error("Error searching images:", error);
    throw new Error("Failed to search images");
  }
}
