"use server";

/**
 * Debug utilities for Cloudflare R2 connection issues
 */

export async function debugR2Configuration() {
  const config = {
    R2_ACCESS_ID: process.env.R2_ACCESS_ID ? "✓ Set" : "✗ Missing",
    R2_ACCESS_KEY: process.env.R2_ACCESS_KEY ? "✓ Set" : "✗ Missing", 
    R2_ACCOUNT_ID: process.env.R2_ACCOUNT_ID ? "✓ Set" : "✗ Missing",
    R2_ENDPOINT: process.env.R2_ENDPOINT ? "✓ Set" : "✗ Missing",
    R2_BUCKET: process.env.R2_BUCKET ? "✓ Set" : "✗ Missing",
  };

  console.log("=== R2 Configuration Debug ===");
  console.log("Environment Variables:");
  Object.entries(config).forEach(([key, status]) => {
    console.log(`  ${key}: ${status}`);
  });

  if (process.env.R2_ENDPOINT) {
    console.log(`  R2_ENDPOINT value: ${process.env.R2_ENDPOINT}`);
  }
  if (process.env.R2_ACCOUNT_ID) {
    console.log(`  R2_ACCOUNT_ID value: ${process.env.R2_ACCOUNT_ID}`);
  }
  if (process.env.R2_BUCKET) {
    console.log(`  R2_BUCKET value: ${process.env.R2_BUCKET}`);
  }

  return config;
}

export async function testR2Connection() {
  try {
    console.log("=== Testing R2 Connection ===");
    
    // First check configuration
    await debugR2Configuration();
    
    // Try to import and test the R2 client
    const { listFiles } = await import("@/lib/cloudflare/cloudflare");
    
    console.log("Attempting to list files...");
    const files = await listFiles();
    
    console.log("✅ R2 Connection successful!");
    console.log(`Found ${files.length} files in bucket`);
    
    return { success: true, fileCount: files.length };
  } catch (error) {
    console.error("❌ R2 Connection failed:");
    console.error("Error name:", error instanceof Error ? error.name : 'Unknown');
    console.error("Error message:", error instanceof Error ? error.message : 'Unknown error');
    console.error("Error code:", (error as any)?.code || 'No code');
    console.error("Error syscall:", (error as any)?.syscall || 'No syscall');
    console.error("Full error:", error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      errorCode: (error as any)?.code,
      errorSyscall: (error as any)?.syscall
    };
  }
}

export async function testSimpleR2Upload() {
  try {
    console.log("=== Testing Simple R2 Upload ===");
    
    // Create a simple test file
    const testContent = "Hello, R2! This is a test file.";
    const testBuffer = Buffer.from(testContent, 'utf-8');
    const testKey = `test/test-${Date.now()}.txt`;
    
    const { uploadFile } = await import("@/lib/cloudflare/cloudflare");
    
    console.log("Uploading test file...");
    const result = await uploadFile(testBuffer, testKey, 'text/plain');
    
    console.log("✅ Test upload successful!");
    console.log("Upload result:", result);
    
    return { success: true, result };
  } catch (error) {
    console.error("❌ Test upload failed:");
    console.error("Error details:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      code: (error as any)?.code,
      syscall: (error as any)?.syscall,
      errno: (error as any)?.errno
    });
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
